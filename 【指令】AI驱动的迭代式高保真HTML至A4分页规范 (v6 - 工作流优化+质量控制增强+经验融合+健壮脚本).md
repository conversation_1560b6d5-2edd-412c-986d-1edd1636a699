# **【指令】AI驱动的迭代式高保真HTML至A4分页规范 (v6 - 工作流优化+质量控制增强+经验融合+健壮脚本)**

## 0. **核心执行原则：AI即分页引擎 (至关重要！)**

**本指令的核心在于：AI Agent 您本身，将作为直接的、智能的分页引擎来执行以下所有步骤。您需要运用您自身的理解、分析、计算、内容操作、浏览器验证和迭代能力，逐步完成每一页的构建与切割。**

*   **禁止依赖外部自动化脚本**：**您不应尝试编写或调用一个单一的、自动化的外部脚本来一次性处理整个分页逻辑。** 本指令描述的是一个需要您**亲自、逐步、动态调整**的智能工作流程。
*   **AI的智能与工具结合是关键**：分页的质量高度依赖于您对HTML内容、CSS的理解，以及您在“**试填-验证-调整**”循环中的智能决策，并**必须结合浏览器工具进行实时验证**。**成功的关键在于精确的样式复刻和基于浏览器反馈的细致迭代调整。**
*   **这是一个工作流，而非脚本规范**：将以下步骤视为您需要遵循的思考和操作流程，而不是一个需要您翻译成代码并执行一次的程序。您在每一步都需要主动思考、判断和**执行验证**。

---

## 1. **核心目标与原则**

利用AI Agent的智能分析与处理能力，将单一长HTML源文档，通过**AI主导的、逐页迭代构建、浏览器实时验证与精细调整**的方式，转换为一系列视觉保真、内容连续、严格符合A4规格且通过质量检测的HTML页面片段，为电子书制作提供优质基础。

*   **AI主导**：分页的核心逻辑和操作由AI Agent**直接执行**。
*   **迭代构建与验证 (“试填-验证-调整”模式)**：每一页A4的内容都通过“**初步填充(基于估算)-浏览器精确验证-质量评分-量化调整(有重试限制)**”的循环来确定，确保最佳切割和质量。**强调小步调整，避免大幅度增删内容导致效果波动。**
*   **高保真性**：严格保持源文档的样式、布局和内容完整性。**必须精确复刻源CSS，包括字体、颜色、间距、对齐等所有细节。**
*   **A4精确性**：严格遵守A4尺寸（210mm×297mm）和边距设定（如20mm）。
*   **质量控制**：每页必须通过自动化质量检测（空间利用率、内容完整性、样式一致性），达到预设评分标准（≥85分）。
*   **章节规则**：**主要章节标题（根据指定选择器识别）必须强制在新页面顶部开始**，符合专业排版规范。

## 2. **输入规范**

*   **源文件 (source.html)**：包含所有内容和样式的HTML文档。
*   **A4页面配置**：
    *   方向：纵向。
    *   DPI：96 DPI。
    *   边距：上、下、左、右（例如，均为20mm）。
    *   **章节标题选择器**：CSS选择器，用于识别章节大标题（例如 `h1`, `h2.chapter-title`）。
    *   **内容容器选择器**：CSS选择器，用于JS脚本测量内容高度（默认 `.report-container` 或 `body`）。

## 3. **输出规范**

*   一系列独立的、序列化的HTML文件（`page_001.html`, `page_002.html`, ...），存储在工作目录下的 `./output` 文件夹中，每个文件代表一个通过质量检测的A4页面。
*   每个输出文件包含该A4页面承载的HTML片段及必要的CSS（建议链接到统一的CSS文件或在`<head>`中包含所有相关样式）。
*   （可选）整体质量报告。

## 4. **AI分页工作流：逐页迭代构建与质量控制 (优化版)**

AI Agent需遵循以下详细步骤，为源文档的每一页进行构建、验证和内容分配：

### 4.0. **初始化与全局状态管理**

*   **计算A4内容区尺寸**：根据A4总尺寸、DPI和边距，计算内容区域的精确像素高度 (`pageContentHeight`)。
*   **加载源文档**：将源HTML文档内容加载到AI的工作内存中。
*   **提取参考样式 `globalReferenceStyles`**：
    *   **首次执行时**：分析源文档，**执行第6节脚本的 `extractCurrentPageStyles` 功能**获取参考样式，并**存储在内存变量 `refStyles` 中**。
    *   **后续页面**：直接使用内存变量 `refStyles`。
*   **内容指针**：维护一个指针，指向源HTML中当前未分配内容的起始位置。
*   **当前页码**：`currentPageNumber = 1`。
*   **质量报告列表**：`qualityReports = []`。

### 4.1. **开始构建新的一页A4 (第 `currentPageNumber` 页)**

1.  **创建临时页面文件**：例如在工作目录下创建 `./output/temp_page_${currentPageNumber}.html`。
2.  **准备页面模板**：写入基本的HTML结构（`<html>`, `<head>`, `<body>`）和A4尺寸、边距的CSS。**确保包含或链接了所有必要的源CSS样式以实现高保真复刻。**
3.  **重置当前页面状态**：`currentPageUsedHeightEstimate = 0` (基于估算的高度), `adjustmentAttempts = 0` (调整次数计数器), `lastAddedElementRef = null` (用于追踪最后添加的元素以便回退)。
4.  **检查章节标题 (强制分页规则)**：
    *   分析“内容指针”处的下一个可见元素。
    *   如果该元素匹配**章节标题选择器**：
        *   **且** `currentPageNumber > 1` 并且当前临时页面`<body>`内已有内容（即不是页首）：则**当前页构建完成**（无需再添加此标题），**直接进入步骤 4.2.B 进行验证**。
        *   **否则**（是第一页，或是新页的第一个元素）：将此章节标题元素添加到临时页面的`<body>`中。
        *   估算其高度并更新 `currentPageUsedHeightEstimate`。
        *   记录此元素为 `lastAddedElementRef`。
        *   将“内容指针”移过该标题。

### 4.2. **迭代填充与验证循环 (“试填-验证-调整”模式)**

此步骤是一个循环，AI持续执行直到当前A4页面被判定为“合格”或达到调整次数上限。

1.  **初步填充 (Tentative Fill based on Estimation)**：
    *   从“内容指针”处选择下一个元素/文本片段。
    *   **AI估算其高度** (`estimatedElementHeight`)。
    *   **决策是否添加**：
        *   如果 `currentPageUsedHeightEstimate + estimatedElementHeight <= pageContentHeight * 1.05` (允许少量超调空间，后续验证调整)：
            *   将此元素/片段添加到临时页面的`<body>`中。
            *   更新 `currentPageUsedHeightEstimate += estimatedElementHeight`。
            *   **记录此元素引用到 `lastAddedElementRef`**。
            *   将“内容指针”移过该元素/片段。
            *   **继续循环 (返回 4.2.1)**。
        *   如果添加后**估算会显著溢出** (`currentPageUsedHeightEstimate + estimatedElementHeight > pageContentHeight * 1.05`)：
            *   **不添加此元素**。
            *   **进入步骤 4.2.A (结束填充，准备验证)**。

2.  **A. 结束填充，准备验证**：
    *   **场景1：页面估算接近填满，但下一个元素放不下**：认为当前页面内容已初步确定，**进入步骤 4.2.B (浏览器验证)**。
    *   **场景2：页面估算未满，但遇到不可分割元素**：认为当前页面内容已初步确定，**进入步骤 4.2.B (浏览器验证)**。
    *   **场景3：页面估算未满，下一个元素是可切分文本**：
        *   计算估算的剩余高度 `remainingHeightEstimate = pageContentHeight - currentPageUsedHeightEstimate`。
        *   AI估算并**精确切分**该文本（优先按句子，其次标点，避免单词截断），将可容纳部分添加到临时页面。
        *   更新 `currentPageUsedHeightEstimate`。
        *   记录部分文本添加信息到 `lastAddedElementRef`。
        *   将“内容指针”移动到切分点。
        *   **进入步骤 4.2.B (浏览器验证)**。
    *   **场景4：源文档内容已处理完毕**：
        *   **进入步骤 4.2.B (浏览器验证)**。

3.  **B. 浏览器精确验证与质量检测 (Render, Measure & Detect)**：
    *   **保存临时文件**：确保所有内容已写入 `./output/temp_page_${currentPageNumber}.html`。
    *   **构造绝对路径**：AI Agent需获取当前工作目录（例如通过 `pwd` 命令），并将相对路径 `./output/temp_page_${currentPageNumber}.html` 拼接为绝对路径。
    *   **浏览器渲染**：使用 `browser_navigate` 工具访问该**绝对路径**。
    *   **执行自动化检测**：
        *   **准备参数**：
            *   `referenceStylesJSON`: 如果 `currentPageNumber > 1`，将内存变量 `refStyles` 转换为JSON字符串；否则传递 `null`。
            *   `contentContainerSelector`: 使用输入规范中定义的“内容容器选择器”。
        *   **调用脚本**：使用 `browser_console_exec` **一次性执行第6节中提供的完整JavaScript脚本块**，并将 `referenceStylesJSON` 和 `contentContainerSelector` 作为参数传递给脚本。
        *   **获取结果**：脚本将返回一个包含所有检测结果的JSON字符串或错误信息。
    *   **解析检测结果**：
        *   如果返回错误信息，记录错误，标记为问题页，**进入步骤 4.3**。
        *   否则，解析返回的JSON字符串，获取 `utilizationRate`, `contentIntegrity`, `styleConsistency`, 和 `qualityScore` 等详细信息。
    *   **记录检测结果**：保存本页的详细检测数据和评分。
    *   **进入步骤 4.2.C (评估与决策)**。

4.  **C. 评估与决策 (Evaluate & Decide - 量化调整与重试限制)**：
    *   **评估质量评分 `qualityScore.score`**：
        *   **如果评分 ≥ 85分（通过）**：
            *   **页面合格**。
            *   **进入步骤 4.3 (页面定稿)**。
        *   **如果评分 < 85分（需优化）**：
            *   **增加调整次数**：`adjustmentAttempts += 1`。
            *   **检查重试次数**：如果 `adjustmentAttempts > 3`：
                *   标记此页为问题页，记录最终评分和原因。
                *   **进入步骤 4.3 (页面定稿 - 作为问题页)**。
            *   **否则 (尝试调整)**：
                *   **分析原因**：根据返回JSON中的 `utilizationRate`, `contentIntegrity.issues`, `styleConsistency.differences` 分析具体问题。
                *   **制定量化调整策略**：
                    *   **空间利用率低 (<90%)**：识别“内容指针”处的**下一个逻辑块**（如 `<p>`, `<ul>`, `<table>`, `<img>`）。将其添加到临时页面的`<body>`末尾。更新“内容指针”。记录此元素为 `lastAddedElementRef`。
                    *   **空间利用率高 (>98%) 或 内容截断**：使用 `lastAddedElementRef` 找到**最后一个添加的元素/文本片段**。将其从临时页面的`<body>`中移除。将“内容指针”回退到该元素之前。重置 `lastAddedElementRef` 为移除前的上一个元素。
                    *   **样式不一致**：记录 `styleConsistency.differences` 中的问题。**不进行自动调整**，标记供后续人工检查。如果其他指标合格，可视为通过但需注意。
                *   **执行调整**：修改临时页面的内容和“内容指针”。
                *   **返回步骤 4.2.B 重新验证**。

### 4.3. **页面定稿与存储**

1.  **处理结果**：
    *   如果页面合格（评分≥85 或 样式问题但其他合格）：将 `./output/temp_page_${currentPageNumber}.html` 重命名为 `./output/page_${currentPageNumber}.html`。
    *   如果页面是问题页（调整超限 或 JS执行错误）：保留为 `./output/problem_page_${currentPageNumber}.html`，或按需处理。
2.  **记录质量报告**：将本页的最终质量报告（包含检测结果、评分、是否问题页及原因）添加到 `qualityReports` 列表。
3.  **更新状态**：`currentPageNumber += 1`。
4.  **检查是否结束**：如果“内容指针”未到源文档末尾，**返回步骤 4.1 开始构建下一页**。
5.  **如果内容已处理完毕**：**进入步骤 4.4 (全部分页完成)**。

### 4.4. **全部分页完成**

1.  **生成整体报告**：调用 `generateOverallQualityReport(qualityReports)`（见第7节代码）。
2.  **输出结果**：提供所有生成的 `./output/page_XXX.html` 文件、问题页面列表和整体质量报告。
3.  **报告异常**：明确指出哪些页面是问题页及其原因。

## 5. **AI能力要求与假设 (更新)**

*   **HTML/CSS理解**：能够解析结构，理解关键CSS属性对布局和尺寸的影响。
*   **文本处理**：估算文本高度，按指定策略（句子、标点）切分，避免单词截断。
*   **文件操作**：读写HTML文件，处理相对路径和绝对路径，**构造绝对路径**。
*   **迭代与逻辑控制**：严格遵循工作流，管理状态（包括`adjustmentAttempts`, `lastAddedElementRef`）。
*   **浏览器工具使用**：熟练使用 `browser_navigate` 和 `browser_console_exec` 执行页面渲染和**单次执行完整的JavaScript检测脚本（包括传递多个参数）**。
*   **智能决策**：根据质量检测结果动态调整分页策略，执行**量化调整**，处理重试限制。
*   **状态管理**：能够管理 `refStyles` 对象，并在需要时将其序列化传递给浏览器脚本。
*   **复杂布局局限性认知**：理解当前流程对标准文档流效果最佳，处理复杂CSS（多列、浮动、Flex/Grid换行）可能存在局限，并在报告中体现。

## 6. **自动化检测JavaScript代码 (单一脚本块 - v3 优化版)**

**AI Agent在步骤4.2.B中，应将以下整个代码块作为字符串传递给 `browser_console_exec` 工具执行，并将 `referenceStylesJSON` 和 `contentContainerSelector` 作为参数传递。**

```javascript
(function(referenceStylesJSON, contentContainerSelector) { // Accept reference styles JSON and container selector
    const results = {
        utilizationRate: 0,
        contentIntegrity: { complete: false, issues: ['Not run'] },
        styleConsistency: { consistent: false, differences: ['Not run'] },
        qualityScore: { score: 0, breakdown: {} },
        error: null
    };

    try {
        // --- Robust Helper Functions (Definitions - Optimized) ---
        function safeQuerySelector(selector, context = document) {
            if (!selector) return null;
            try {
                return context.querySelector(selector);
            } catch (e) {
                console.warn(`Error querying selector "${selector}":`, e);
                return null;
            }
        }
        function safeQuerySelectorAll(selector, context = document) {
            if (!selector) return [];
            try {
                return Array.from(context.querySelectorAll(selector));
            } catch (e) {
                console.warn(`Error querying selectorAll "${selector}":`, e);
                return [];
            }
        }

        function getElementHeight(element) {
            // ... (保持v2版本，或根据需要进一步优化) ...
            if (!element) return 0;
            try {
                if (element.offsetParent === null && element.getBoundingClientRect().width === 0 && element.getBoundingClientRect().height === 0) {
                    return 0;
                }
                return element.getBoundingClientRect().height;
            } catch (e) {
                console.warn("Error getting element height:", e);
                return 0;
            }
        }

        function measureUtilizationRate(containerSelector) {
            try {
                const defaultSelector = ".report-container";
                const selectorToUse = typeof containerSelector === 'string' && containerSelector.trim() !== '' ? containerSelector : defaultSelector;
                let contentElement = safeQuerySelector(selectorToUse);
                let measuredHeight = 0;

                if (contentElement) {
                    measuredHeight = getElementHeight(contentElement);
                } else {
                    console.warn(`Container "${selectorToUse}" not found. Falling back to document.body.`);
                    contentElement = document.body;
                    measuredHeight = getElementHeight(contentElement); // Or document.documentElement.scrollHeight?
                }

                if (measuredHeight === 0 && document.body.children.length > 0) {
                     console.warn(`Measured height of container (${selectorToUse || 'body'}) is zero. Check content visibility or selector.`);
                     // Consider alternative fallback: sum of direct children heights?
                }

                const availableHeightMm = 297 - 40; // A4 height(mm) minus top/bottom margins (e.g., 20mm each)
                const mmToPxRatio = 3.78; // Assuming 96 DPI
                const availableHeightPx = availableHeightMm * mmToPxRatio;
                if (availableHeightPx <= 0) {
                    throw new Error("Calculated available height is zero or negative.");
                }
                const utilizationRate = measuredHeight / availableHeightPx;
                console.log(`Content Height: ${measuredHeight.toFixed(1)}px, Available Height: ${availableHeightPx.toFixed(1)}px, Utilization Rate: ${(utilizationRate * 100).toFixed(1)}%`);
                return parseFloat(utilizationRate.toFixed(3));
            } catch (error) {
                console.error("Error in measureUtilizationRate:", error);
                results.error = `Measure Util Rate Error: ${error.message}`;
                return 0;
            }
        }

        function verifyContentIntegrity() {
            let issues = [];
            const issueTypes = { TRUNCATED_WORD: 0, TRUNCATED_NO_PUNCT: 0, EMPTY_ELEMENT: 0, BROKEN_IMAGE: 0 };
            try {
                // Check for truncated text
                safeQuerySelectorAll("p, li > span, div").forEach((el, index) => {
                    // ... (v2 截断检查逻辑，可进一步优化，例如区分警告和错误)
                    try {
                        let hasTextNode = false;
                        el.childNodes.forEach(node => { if (node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0) hasTextNode = true; });
                        if (!hasTextNode) return;
                        const text = el.textContent?.trim();
                        if (text && text.length > 0) {
                            const isLastVisibleElementInParent = (!el.nextElementSibling || el.nextElementSibling.offsetParent === null);
                            if (!/[\.,!?;:。！？；：）\)）\]】'"”]$/.test(text.replace(/\s+$/, ''))) {
                                if (text.length > 1 && text.slice(-1).match(/[a-zA-Z0-9]/) && text.slice(-2, -1).match(/[a-zA-Z0-9]/)) {
                                    issues.push({ type: 'TRUNCATED_WORD', element: `${el.tagName} #${index + 1}`, text: `...${text.slice(-40)}` });
                                    issueTypes.TRUNCATED_WORD++;
                                } else if (!el.closest("li") || (el.closest("li") && !isLastVisibleElementInParent && text.length > 10)) {
                                    issues.push({ type: 'TRUNCATED_NO_PUNCT', element: `${el.tagName} #${index + 1}`, text: `...${text.slice(-40)}` });
                                    issueTypes.TRUNCATED_NO_PUNCT++;
                                }
                            }
                        }
                    } catch (e) { issues.push({ type: 'CHECK_ERROR', element: `${el.tagName} #${index + 1}`, message: e.message }); }
                });
                // Check for empty elements
                safeQuerySelectorAll("h1, h2, h3, h4, h5, h6, li, th, td").forEach((el, index) => {
                    // ... (v2 空元素检查逻辑)
                    try {
                        if (el.textContent?.trim().length === 0 && el.children.length === 0 && !safeQuerySelector('img, input, br', el)) {
                            issues.push({ type: 'EMPTY_ELEMENT', element: `${el.tagName.toLowerCase()} #${index + 1}` });
                            issueTypes.EMPTY_ELEMENT++;
                        }
                    } catch (e) { issues.push({ type: 'CHECK_ERROR', element: `${el.tagName.toLowerCase()} #${index + 1}`, message: e.message }); }
                });
                // Check for broken images
                safeQuerySelectorAll("img").forEach((img, index) => {
                    // ... (v2 坏图检查逻辑)
                    try {
                        if (!img.complete || img.naturalHeight === 0) {
                            issues.push({ type: 'BROKEN_IMAGE', element: `Image #${index + 1}`, src: img.src.substring(0, 50) });
                            issueTypes.BROKEN_IMAGE++;
                        }
                    } catch (e) { issues.push({ type: 'CHECK_ERROR', element: `Image #${index + 1}`, message: e.message }); }
                });
            } catch (error) {
                console.error("Error during content integrity check:", error);
                issues.push({ type: 'GENERAL_ERROR', message: error.message });
                results.error = `Content Integrity Error: ${error.message}`;
            }
            console.log(`Content Integrity Check: ${issues.length === 0 ? "Passed" : JSON.stringify(issueTypes)} issues found.`);
            return { complete: issues.length === 0, issues: issues, issueCounts: issueTypes };
        }

        function extractCurrentPageStyles() {
            // ... (v2 样式提取逻辑，可考虑采样多个元素)
            const styles = {};
            const elementsToStyle = {
                "h1": "h1", "h2": "h2", "h3": "h3", "h4": "h4",
                "p": "p", "ul": "ul", "ol": "ol", "li": "li",
                "a": "a", "strong": "strong", "em": "em",
                "blockquote": "blockquote", "table": "table", "th": "th", "td": "td",
                "pre": "pre", "code": "code", "figure": "figure", "figcaption": "figcaption"
            };
            for (const tag in elementsToStyle) {
                const element = safeQuerySelector(elementsToStyle[tag]);
                if (element) {
                    try {
                        const computedStyle = window.getComputedStyle(element);
                        styles[tag] = {
                            fontFamily: computedStyle.fontFamily,
                            fontSize: computedStyle.fontSize,
                            // ... (add more properties as needed)
                            color: computedStyle.color,
                            lineHeight: computedStyle.lineHeight,
                            textAlign: computedStyle.textAlign,
                            marginTop: computedStyle.marginTop,
                            marginBottom: computedStyle.marginBottom,
                        };
                    } catch (e) { console.warn(`Could not get styles for ${tag}:`, e); }
                }
            }
            return styles;
        }

        function normalizeStyleValue(prop, value) {
            // ... (v2 样式标准化逻辑，可增强)
            if (value === null || value === undefined) return null;
            value = String(value).trim().toLowerCase();
            if (prop.toLowerCase().includes('color')) {
                // Basic color normalization (needs improvement for rgba, hsl etc.)
                if (value.startsWith('#') && value.length === 4) { // #rgb to #rrggbb
                    value = '#' + value[1] + value[1] + value[2] + value[2] + value[3] + value[3];
                }
                // Add mapping for common color names to hex/rgb if needed
            } else if (prop.toLowerCase().includes('margin') || prop.toLowerCase().includes('padding') || prop.toLowerCase().includes('fontsize') || prop.toLowerCase().includes('lineheight')) {
                // Basic unit normalization (px)
                if (value.endsWith('pt')) value = parseFloat(value) * 1.33 + 'px';
                if (value.endsWith('em') || value.endsWith('rem')) { /* Needs context for conversion */ }
                // Keep 'px' or convert others if possible
            }
            return value;
        }

        function compareStyles(reference, current) {
            // ... (v2 样式比较逻辑，可细化容差)
            const differences = [];
            if (!reference || typeof reference !== 'object') {
                console.warn("Reference styles are missing or invalid.");
                return { consistent: true, differences: ['No reference styles provided'] }; // Assume consistent if no reference
            }
            if (!current || typeof current !== 'object') {
                return { consistent: false, differences: ['Could not extract current page styles'] };
            }
            let diffCount = 0;
            try {
                for (const tag in reference) {
                    if (!current[tag]) {
                        differences.push({ tag: tag, issue: 'Missing in current' }); diffCount++; continue;
                    }
                    for (const prop in reference[tag]) {
                        if (reference[tag].hasOwnProperty(prop)) {
                            const refValue = reference[tag][prop];
                            const curValue = current[tag] ? current[tag][prop] : undefined;
                            const normalizedRef = normalizeStyleValue(prop, refValue);
                            const normalizedCur = normalizeStyleValue(prop, curValue);
                            if (normalizedRef !== null && normalizedRef !== undefined && normalizedCur !== normalizedRef) {
                                // Add simple tolerance for numeric values like line-height, font-size?
                                let isDifferent = true;
                                if (!isNaN(parseFloat(normalizedRef)) && !isNaN(parseFloat(normalizedCur))) {
                                    if (Math.abs(parseFloat(normalizedRef) - parseFloat(normalizedCur)) < 1) { // Example tolerance: 1px
                                        isDifferent = false;
                                    }
                                }
                                if (isDifferent) {
                                    differences.push({ tag: tag, property: prop, expected: normalizedRef, actual: normalizedCur });
                                    diffCount++;
                                }
                            }
                        }
                    }
                }
            } catch (error) {
                console.error("Error during style comparison:", error);
                differences.push({ type: 'GENERAL_ERROR', message: error.message });
                results.error = `Style Compare Error: ${error.message}`;
                diffCount++;
            }
            console.log(`Style Consistency Check: ${diffCount === 0 ? "Passed" : diffCount + " differences found"}`);
            return { consistent: diffCount === 0, differences: differences };
        }

        function calculatePageQualityScore(utilization, integrity, consistency) {
            // ... (v2 评分逻辑，可调整权重)
            const weights = { utilization: 0.4, integrity: 0.3, consistency: 0.3 };
            let score = 0;
            let breakdown = {};

            // Utilization Score (Ideal range 0.90 - 0.98)
            let utilScore = 0;
            if (utilization >= 0.90 && utilization <= 0.98) utilScore = 100;
            else if (utilization > 0.85 && utilization < 0.90) utilScore = 80;
            else if (utilization > 0.98 && utilization <= 1.0) utilScore = 70; // Slightly over is better than under
            else if (utilization > 1.0) utilScore = 50; // Penalize overflow
            else utilScore = Math.max(0, utilization / 0.90 * 80); // Linear scale up to 80 for underflow
            score += utilScore * weights.utilization;
            breakdown.utilization = utilScore.toFixed(0);

            // Integrity Score (Penalize based on issue severity)
            let integrityScore = 100;
            if (!integrity.complete) {
                integrity.issues.forEach(issue => {
                    if (issue.type === 'TRUNCATED_WORD') integrityScore -= 20; // High penalty
                    else if (issue.type === 'TRUNCATED_NO_PUNCT') integrityScore -= 10;
                    else if (issue.type === 'BROKEN_IMAGE') integrityScore -= 15;
                    else if (issue.type === 'EMPTY_ELEMENT') integrityScore -= 5;
                    else integrityScore -= 2; // Minor penalty for other errors
                });
            }
            integrityScore = Math.max(0, integrityScore);
            score += integrityScore * weights.integrity;
            breakdown.integrity = integrityScore.toFixed(0);

            // Consistency Score
            let consistencyScore = consistency.consistent ? 100 : Math.max(0, 100 - consistency.differences.length * 10); // Penalize per difference
            score += consistencyScore * weights.consistency;
            breakdown.consistency = consistencyScore.toFixed(0);

            score = Math.max(0, Math.min(100, Math.round(score)));
            console.log(`Quality Score: ${score}, Breakdown: U=${breakdown.utilization}, I=${breakdown.integrity}, C=${breakdown.consistency}`);
            return { score: score, breakdown: breakdown };
        }

        // --- Main Execution Logic ---
        let referenceStyles = null;
        if (referenceStylesJSON) {
            try {
                referenceStyles = JSON.parse(referenceStylesJSON);
            } catch (e) {
                console.warn("Could not parse referenceStylesJSON:", e);
                results.error = "Invalid reference styles JSON";
                // Decide if we should proceed without reference styles or fail
            }
        }
        // If it's the first page or reference styles failed to parse, extract from current page
        if (!referenceStyles) {
            console.log("Extracting reference styles from current page (Page 1 or fallback).");
            referenceStyles = extractCurrentPageStyles();
            // Optionally, return these styles if needed by the agent for future pages?
        }

        const currentPageStyles = extractCurrentPageStyles();

        results.utilizationRate = measureUtilizationRate(contentContainerSelector);
        results.contentIntegrity = verifyContentIntegrity();
        results.styleConsistency = compareStyles(referenceStyles, currentPageStyles);
        results.qualityScore = calculatePageQualityScore(results.utilizationRate, results.contentIntegrity, results.styleConsistency);

    } catch (generalError) {
        console.error("General error during quality check execution:", generalError);
        results.error = `General Execution Error: ${generalError.message}`;
        // Ensure default values are set for scores if error occurs early
        results.qualityScore = { score: 0, breakdown: {} };
    }

    // Return results as a JSON string
    return JSON.stringify(results);

})(arguments[0], arguments[1]); // Pass referenceStylesJSON and contentContainerSelector as arguments
```

## 7. **辅助JavaScript函数 (用于整体报告)**

```javascript
function generateOverallQualityReport(qualityReports) {
    if (!Array.isArray(qualityReports) || qualityReports.length === 0) {
        return "No quality reports available.";
    }
    let totalPages = qualityReports.length;
    let passedPages = 0;
    let problemPages = 0;
    let avgScore = 0;
    let issuesSummary = {};
    let problemPageDetails = [];

    qualityReports.forEach((report, index) => {
        const pageNum = index + 1;
        if (report.error) {
            problemPages++;
            problemPageDetails.push(`Page ${pageNum}: Execution Error - ${report.error}`);
        } else if (report.qualityScore && report.qualityScore.score >= 85) {
            passedPages++;
            avgScore += report.qualityScore.score;
        } else {
            problemPages++;
            const reason = report.qualityScore ? `Score ${report.qualityScore.score} (<85)` : 'Score unavailable';
            problemPageDetails.push(`Page ${pageNum}: Failed - ${reason}`);
            // Summarize issues
            if (report.contentIntegrity && report.contentIntegrity.issues) {
                report.contentIntegrity.issues.forEach(issue => {
                    issuesSummary[issue.type] = (issuesSummary[issue.type] || 0) + 1;
                });
            }
            if (report.styleConsistency && report.styleConsistency.differences) {
                issuesSummary['STYLE_DIFFERENCE'] = (issuesSummary['STYLE_DIFFERENCE'] || 0) + report.styleConsistency.differences.length;
            }
        }
    });

    avgScore = passedPages > 0 ? (avgScore / passedPages).toFixed(1) : 'N/A';

    let reportString = `--- Overall Quality Report ---\n`;
    reportString += `Total Pages Processed: ${totalPages}\n`;
    reportString += `Passed Pages (Score >= 85): ${passedPages}\n`;
    reportString += `Problem/Failed Pages: ${problemPages}\n`;
    reportString += `Average Score (Passed Pages): ${avgScore}\n\n`;

    if (problemPages > 0) {
        reportString += `Problem Page Details:\n`;
        problemPageDetails.forEach(detail => reportString += `- ${detail}\n`);
        reportString += `\nIssue Summary (Across Failed Pages):\n`;
        for (const issueType in issuesSummary) {
            reportString += `- ${issueType}: ${issuesSummary[issueType]} occurrences\n`;
        }
    }

    return reportString;
}
```

## 8. **复杂布局与局限性说明**

*   **当前流程最适用于标准文档流布局**（如报告、文章）。
*   **复杂CSS处理局限**：对于多列布局、大量浮动元素、复杂的Flexbox/Grid换行场景，自动化高度测量和内容完整性检测可能不完全准确。AI Agent应在遇到此类页面时谨慎处理，并在报告中注明潜在问题。
*   **动态内容/脚本**：此流程不适用于包含大量客户端脚本动态生成内容的页面。

---

**【指令结束】AI Agent请严格遵循以上工作流和原则执行分页任务。**
