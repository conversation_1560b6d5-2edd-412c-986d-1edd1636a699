:root {
    --font-size-body: 15px;
    --font-size-h5: 15px;       
    --font-size-h4: 18px;    
    --font-size-h3: 22px;     
    --font-size-h2: 28px;    
    --font-size-h1: 40px;    
    --color-text: #333333;
    --color-h1: #7BA98C;
    --color-h2: #8DBB9E;
    --color-emphasis-1: #ACDA8C;
    --color-emphasis-2: #C5E1B3;
    --color-bg-light: #F0F7ED;
    --primary-color: #05668d;
    --secondary-color: #427aa1;
    --background-color: #ebf2fa;
    --accent-color: #679436;
    --highlight-color: #a5be00;
    --text-primary: #2c3e50;
    --text-secondary: #427aa1;
    --text-light: #ebf2fa;
    --border-color: #427aa1;
}
body {
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: "Source Han Sans CN", "思源黑体 CN", sans-serif;
    font-size: var(--font-size-body);
    color: var(--text-primary);
    font-style: normal;
    font-weight: 400;
    line-height: 1.8;
    letter-spacing: 0.01em;
    margin: 0 35px 0 35px;
    padding: 40px 0;
    background-color: var(--background-color);
    width: 210mm;
    height: 297mm; /* Note: This height is for a single page. Content overflow will be handled by pagination logic. */
    box-sizing: border-box;
    padding: 20mm; /* Setting body padding to match @page margin */
    margin: 0;
}
.container {
    max-width: 800px;
    margin: 0px auto;
    padding: 40px 35px;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}
.chart-container {
    position: relative;
    margin: 3em auto;
    max-width: 500px;
    height: 400px;
    overflow: visible;
}





.report-container {
max-width: 800px;
margin: 2em auto;
padding: 3em; /* A4纸张感内边距 */
background-color: #ffffff; /* 内容区白色 */
box-shadow: 0 0 20px rgba(0, 0, 0, 0.05); /* 轻微阴影增加层次感 */
border-radius: 4px; /* 轻微圆角 */
}

h1, h2, h3, h4 {
font-family: 'Source Han Sans CN Bold', '思源黑体 CN Bold', sans-serif; /* 标题使用更粗的字重 */
margin-top: 2em;
margin-bottom: 1em;
line-height: 1.3;
letter-spacing: -0.02em; /* 轻微收紧字间距 */
}

h1 { /* 文档主标题 */
font-size: var(--font-size-h1); /* 40px */
font-weight: 700; /* Bold */
text-align: center;
margin-bottom: 1.5em;
border-bottom: 2px solid var(--color-h1);
padding-bottom: 0.5em;
color: var(--color-h1);
}

h2 { /* 一级章节标题 */
font-size: var(--font-size-h2); /* 28px */
font-weight: 600; /* Medium to Bold */
border-bottom: 1px solid #ccc;
padding-bottom: 0.3em;
color: var(--color-h1);
}

h3 { /* 二级章节标题 */
font-size: var(--font-size-h3); /* 22px */
font-weight: 500; /* Medium */
color: var(--color-h2);
}

h4 { /* 三级章节标题 */
font-size: var(--font-size-h4); /* 18px */
font-weight: 500; /* Medium */
color: var(--color-h2);
}

p {
margin-bottom: 1.2em;
margin-top: 0.8em;
text-align: justify;
line-height: 1.8;
letter-spacing: 0.01em;
font-weight: 400;
}

ul, ol {
margin-bottom: 1.2em;
padding-left: 1.5em;
line-height: 1.7;
}

li {
margin-bottom: 0.5em;
}

blockquote {
    border-left: 4px solid var(--color-emphasis-1);
    padding: 1.2em 1.8em;
    margin: 1.8em 0;
    background-color: var(--color-bg-light);
    color: var(--color-text);
    font-style: italic;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 4px rgba(172, 218, 140, 0.1);
}



a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

a:hover {
    color: var(--secondary-color);
    text-decoration: none;
}

a:hover::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: var(--color-emphasis-2);
    transform: scaleX(1);
    transition: transform 0.3s ease;
}

table {
width: 100%;
border-collapse: collapse;
margin-bottom: 1.5em;
font-size: 0.95em;
}

th, td {
border: 1px solid #dee2e6;
padding: 0.75em;
text-align: left;
vertical-align: top;
}

th {
background-color: #f2f2f2;
font-family: 'Source Han Sans CN Medium', '思源黑体 CN Medium', sans-serif;
font-weight: 500;
color: var(--color-h2);
}



/* 图表容器样式 */
.chart-container {
width: 100%;
max-width: 700px; /* 最大图表宽度 */
height: 450px; /* 给canvas一个固定高度 */
margin: 2em auto; /* 上下间距并居中 */
padding: 1em;
border: 1px solid #eee;
border-radius: 4px;
background-color: #fff;
box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 图片建议样式 */
.image-suggestion {
display: block;
margin: 2em auto;
padding: 1.2em;
border: 1px dashed #ccc;
background-color: var(--color-bg-light);
text-align: center;
font-style: italic;
color: #555;
}

.image-suggestion img {
max-width: 100%;
height: auto;
margin-top: 0.8em;
border: 1px solid #ddd;
}

figcaption {
font-size: 0.8em;
color: #7F8896;
text-align: center;
margin-top: 0.8em;
font-style: normal;
font-family: 'Source Han Sans CN Light', '思源黑体 CN Light', sans-serif;
}

/* 封面特定样式 */
.cover-page {
text-align: center;
margin-bottom: 3em;
padding: 3em 0;
border-bottom: 1px solid #eee;
}
.cover-page h1 {
font-size: var(--font-size-h1);
margin-bottom: 1.2em;
border-bottom: none;
color: var(--color-h1);
letter-spacing: -0.02em;
}
.cover-page p {
font-size: 1.1em;
margin-bottom: 0.7em;
text-align: center;
line-height: 1.7;
}



/* 目录特定样式 */
#table-of-contents {
margin-top: 2em;
margin-bottom: 3em;
padding: 2em;
background-color: transparent;
border: none;
border-radius: 12px;
box-shadow: none;
}
#table-of-contents h2 {
font-size: 1.6em;
margin-top: 0;
border-bottom: none;
color: var(--color-h1);
margin-bottom: 1em;
letter-spacing: -0.02em;
}
#table-of-contents ul {
list-style-type: none;
padding-left: 0;
}
#table-of-contents ul li {
margin-bottom: 0.8em;
line-height: 1.6;
letter-spacing: 0.01em;
}
#table-of-contents ul li a {
text-decoration: none;
color: var(--color-text);
transition: color 0.3s ease;
font-weight: 400;
}
#table-of-contents ul li a:hover {
color: var(--color-emphasis-1);
}
#table-of-contents ul ul {
padding-left: 1.8em;
margin-top: 0.4em;
}
#table-of-contents > ul > li > a {
font-family: 'Source Han Sans CN Medium', '思源黑体 CN Medium', sans-serif;
font-weight: 500;
letter-spacing: 0.01em;
}


/* 结论与建议的核心表格模板 */
.conclusion-table th, .conclusion-table td {
font-size: 0.9em; /* 调整表格内字体大小以容纳更多内容 */
padding: 0.7em;
}
.conclusion-table th {
background-color: #e9ecef;
color: var(--color-h2);
}
.conclusion-table td ul {
padding-left: 1em;
margin-bottom: 0;
}
.conclusion-table td ul li {
margin-bottom: 0.2em;
font-size: 0.95em;
}
.highlight-recommendation {
    background-color: rgba(172, 218, 140, 0.15);
    border-radius: 6px;
    padding: 1em;
    border-left: 4px solid var(--color-emphasis-1);
}
.rating-high { color: var(--color-emphasis-1); font-weight: bold; }
.rating-medium-high { color: var(--color-h2); font-weight: bold;}
.rating-medium { color: var(--color-emphasis-2); font-weight: bold; }


/* 页眉页脚（模拟） */
@media print {
    .page-header, .page-footer {
        position: fixed;
        width: 100%;
        font-size: 0.8em;
        color: #777;
        font-family: 'Source Han Sans CN Light', '思源黑体 CN Light', sans-serif;
    }
    .page-header {
        top: 0;
        text-align: right;
    }
    .page-footer {
        bottom: 0;
        text-align: center;
    }
    .page-break {
        page-break-after: always;
    }
    /* 确保报告容器在打印时不分页内部（尽可能） */
    .report-container {
        box-shadow: none;
        margin: 0;
        border-radius: 0;
    }
    /* 打印时的颜色处理 */
    h1, h2, h3, h4 {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    a {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    blockquote {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    .highlight-recommendation {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
}



@media only screen and (max-device-width: 768px) {
body {
    padding: 20px 0;
    margin: 0;
    font-family: "Source Han Sans CN", "思源黑体 CN", sans-serif;
    font-size: 15px;
    line-height: 1.7;
}

.container {
    padding: 20px;
    margin: 16px 20px 30px;
    box-shadow: none;
}

h1,
h2,
h3,
h4 {
    font-family: "Source Han Sans CN Bold", "思源黑体 CN Bold", sans-serif;
    letter-spacing: -0.01em;
}

h1 {
    font-size: 1.87em;
    line-height: 1.6;
    margin-bottom: 0.8em;
    text-align: center;
    color: var(--color-h1);
}

h2 {
    font-size: 1.6em;
    font-weight: 600;
    margin-top: 1.4em;
    margin-bottom: 0.9em;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5em;
    color: var(--color-h1);
}

h3 {
    font-size: 1.3em;
    font-weight: 600;
    margin-top: 1.2em;
    margin-bottom: 0.7em;
    color: var(--color-h2);
}

h4 {
    font-size: 1.1em;
    font-weight: 500;
    margin-top: 1em;
    margin-bottom: 0.5em;
    font-style: normal;
    color: var(--color-h2);
}

h5 {
    font-size: 1em;
    font-weight: 500;
    margin-bottom: 1.2em;
}

ul,
ol {
    font-size: 1em;
    font-weight: 400;
    margin-bottom: 1.2em;
    line-height: 1.8;
}

p {
    font-size: 1em;
    line-height: 1.8;
    font-weight: 400;
    margin-top: 0.8em;
    margin-bottom: 0.8em;
}

blockquote {
    padding: 1em 1.2em;
    background-color: var(--color-bg-light);
    border-left: 4px solid var(--color-emphasis-1);
}

blockquote p {
    margin: 0;
}

figcaption {
    margin-top: 0.5em;
    font-size: 0.8em;
    font-weight: 400;
    text-align: center;
    font-style: normal;
    color: #7F8896;
    font-family: "Source Han Sans CN Light", "思源黑体 CN Light", sans-serif;
}

img {
    display: block;
    overflow: hidden;
    max-width: 100%;
    max-height: 335px;
    margin: 1.2em auto;
    border-radius: 8px;
}

.chart-container {
    height: 350px;
    margin: 1.5em auto;
}
}

/* 导航栏样式 */
.navbar {
  background-color: var(--primary-color);
  color: var(--text-light);
}

.nav-link {
  color: var(--text-light);
}

.nav-link:hover {
  color: var(--highlight-color);
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #045577;
  border-color: #045577;
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-secondary:hover {
  background-color: #366a8c;
  border-color: #366a8c;
}

.btn-success {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-success:hover {
  background-color: #55832b;
  border-color: #55832b;
}

/* 卡片样式 */
.card {
  background-color: var(--background-color);
  border-color: var(--border-color);
}

.card-header {
  background-color: var(--secondary-color);
  color: var(--text-light);
}

/* 表格样式 */
.table th {
  background-color: var(--secondary-color);
  color: var(--text-light);
}

.table tr:nth-child(even) {
  background-color: var(--background-color);
}

.table tr:nth-child(odd) {
  background-color: #ffffff;
}

.table td, .table th {
  border-color: var(--border-color);
}

/* 状态标签样式 */
.status-success {
  color: var(--accent-color);
}

.status-warning {
  color: var(--highlight-color);
}

.status-error {
  color: #e74c3c;
}

.status-info {
  color: var(--secondary-color);
}

/* 图片建议样式 */
.image-suggestions {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  padding: 1rem;
  margin: 1rem 0;
}

.image-suggestions h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.image-suggestions .suggestion-item h4 {
  color: var(--secondary-color);
}

.image-suggestions .suggestion-item ul li {
  color: var(--text-primary);
  margin: 0.5rem 0;
}

@page {
    size: 210mm 297mm;
    margin: 20mm;
}

.page-break {
    display: block;
    page-break-before: always;
}